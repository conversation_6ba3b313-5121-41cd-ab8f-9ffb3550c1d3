chest_namespace_reader:
    type: task
    debug: false
    script:
    # Get the chest the player is looking at
    - define chest_location <player.cursor_on>

    # Check if the location has an inventory (is a chest or similar container)
    - if !<[chest_location].has_inventory>:
        - narrate "<&c>You must be looking at a chest or container!"
        - stop

    # Get the inventory of the chest
    - define chest_inventory <[chest_location].inventory>

    # Get all items from the chest
    - define chest_items <[chest_inventory].list_contents>

    # Check if chest is empty
    - if <[chest_items].is_empty>:
        - narrate "<&c>The chest is empty!"
        - stop

    # Process each item in the chest and print namespace:id from NBT to console
    - foreach <[chest_items]> as:item:
        # Skip air/empty slots
        - if <[item].material> == air:
            - foreach next

        # Get the NBT data as a map
        - define nbt_data <[item].custom_data||<map>>

        # Check if NBT data exists and has the namespace/id map
        - if !<[nbt_data].is_empty>:
            # Try to get namespace and id from the NBT map
            - define namespace <[nbt_data].get[namespace]||"">
            - define id <[nbt_data].get[id]||"">

            # If both namespace and id exist, print them
            - if <[namespace]> != "" && <[id]> != "":
                - announce to_console <[namespace]>:<[id]>
            - else:
                # Print the entire NBT map to see structure
                - announce to_console "NBT Data: <[nbt_data]>"
        - else:
            # No custom NBT data, print material name as fallback
            - announce to_console "No NBT: <[item].material.name>"

# Enhanced version that searches deeper in NBT structure
chest_nbt_deep_reader:
    type: task
    debug: false
    script:
    # Get the chest the player is looking at
    - define chest_location <player.cursor_on>

    # Check if the location has an inventory
    - if !<[chest_location].has_inventory>:
        - narrate "<&c>You must be looking at a chest or container!"
        - stop

    # Get the inventory of the chest
    - define chest_inventory <[chest_location].inventory>
    - define chest_items <[chest_inventory].list_contents>

    # Check if chest is empty
    - if <[chest_items].is_empty>:
        - narrate "<&c>The chest is empty!"
        - stop

    # Process each item and search for namespace:id in NBT
    - foreach <[chest_items]> as:item:
        # Skip air/empty slots
        - if <[item].material> == air:
            - foreach next

        # Get the NBT data
        - define nbt_data <[item].custom_data||<map>>

        # Print slot number for reference
        - announce to_console "=== Slot <[loop_index]> ==="

        # If NBT data exists, search through it
        - if !<[nbt_data].is_empty>:
            # Print the full NBT structure first
            - announce to_console "Full NBT: <[nbt_data]>"

            # Try different possible locations for namespace and id
            - define found false

            # Check direct keys
            - if <[nbt_data].contains[namespace]> && <[nbt_data].contains[id]>:
                - announce to_console "Found: <[nbt_data].get[namespace]>:<[nbt_data].get[id]>"
                - define found true

            # Check if there's a nested map with namespace/id
            - foreach <[nbt_data].keys> as:key:
                - define value <[nbt_data].get[<[key]>]>
                - if <[value].object_type> == MapTag:
                    - if <[value].contains[namespace]> && <[value].contains[id]>:
                        - announce to_console "Found in <[key]>: <[value].get[namespace]>:<[value].get[id]>"
                        - define found true

            # If not found, list all keys to help debug
            - if !<[found]>:
                - announce to_console "Available keys: <[nbt_data].keys>"
        - else:
            - announce to_console "No NBT data - Material: <[item].material.name>"

# Command to run the script
chest_namespace_command:
    type: command
    name: chestnamespace
    description: Prints namespace:id of each item in chest to console
    usage: /chestnamespace
    permission: denizen.readchest
    script:
    - run chest_namespace_reader

# Command for deep NBT search
chest_nbt_command:
    type: command
    name: chestnbt
    description: Deep search for namespace:id in NBT data
    usage: /chestnbt
    permission: denizen.readchest
    script:
    - run chest_nbt_deep_reader
