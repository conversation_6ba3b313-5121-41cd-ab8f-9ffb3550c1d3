chest_namespace_reader:
    type: task
    debug: false
    script:
    # Get the chest the player is looking at
    - define chest_location <player.cursor_on>

    # Check if the location has an inventory (is a chest or similar container)
    - if !<[chest_location].has_inventory>:
        - narrate "<&c>You must be looking at a chest or container!"
        - stop

    # Get the inventory of the chest
    - define chest_inventory <[chest_location].inventory>

    # Get all items from the chest
    - define chest_items <[chest_inventory].list_contents>

    # Check if chest is empty
    - if <[chest_items].is_empty>:
        - narrate "<&c>The chest is empty!"
        - stop

    # Process each item in the chest and print namespace:id to console
    - foreach <[chest_items]> as:item:
        # Skip air/empty slots
        - if <[item].material> == air:
            - foreach next

        # Get the full material name (includes namespace if present)
        - define full_material_name <[item].material.name>

        # Print to console
        - announce to_console "<[full_material_name]>"

# Command to run the script
chest_namespace_command:
    type: command
    name: chestnamespace
    description: Prints namespace:id of each item in chest to console
    usage: /chestnamespace
    permission: denizen.readchest
    script:
    - run chest_namespace_reader
