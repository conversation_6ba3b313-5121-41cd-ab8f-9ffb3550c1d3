simple_chest_reader:
    type: task
    debug: false
    script:
    # Get the chest the player is looking at
    - define chest_location <player.cursor_on>
    
    # Check if the location has an inventory (is a chest or similar container)
    - if !<[chest_location].has_inventory>:
        - narrate "<&c>You must be looking at a chest or container!"
        - stop
    
    # Get the inventory of the chest
    - define chest_inventory <[chest_location].inventory>
    
    # Get all items from the chest
    - define chest_items <[chest_inventory].list_contents>
    
    # Check if chest is empty
    - if <[chest_items].is_empty>:
        - narrate "<&c>The chest is empty!"
        - stop
    
    # Generate filename with timestamp
    - define timestamp <util.time_now.format[yyyy-MM-dd_HH-mm-ss]>
    - define filename chest_data_<[timestamp]>.yml
    
    # Create YAML file
    - yaml create id:chest_data
    
    # Set basic info
    - yaml set id:chest_data key:timestamp value:<util.time_now>
    - yaml set id:chest_data key:chest_location value:<[chest_location]>
    - yaml set id:chest_data key:chest_world value:<[chest_location].world.name>
    - yaml set id:chest_data key:player value:<player.name>
    - yaml set id:chest_data key:total_slots value:<[chest_items].size>
    
    # Process each item in the chest
    - define item_count 0
    - foreach <[chest_items]> as:item:
        # Skip air/empty slots
        - if <[item].material> == air:
            - foreach next
        
        # Increment counter for non-empty items
        - define item_count <[item_count].add[1]>
        
        # Get item namespace (material namespace)
        - define namespace <[item].material.name.before[:]||minecraft>
        - define material_name <[item].material.name.after[:]||<[item].material.name>>
        
        # Set item data in YAML
        - yaml set id:chest_data key:items.<[loop_index]>.slot value:<[loop_index]>
        - yaml set id:chest_data key:items.<[loop_index]>.material value:<[material_name]>
        - yaml set id:chest_data key:items.<[loop_index]>.namespace value:<[namespace]>
        - yaml set id:chest_data key:items.<[loop_index]>.quantity value:<[item].quantity>
        - yaml set id:chest_data key:items.<[loop_index]>.display_name value:<[item].display||"">
        - yaml set id:chest_data key:items.<[loop_index]>.lore value:<[item].lore||<list>>
        - yaml set id:chest_data key:items.<[loop_index]>.enchantments value:<[item].enchantment_map||<map>>
        - yaml set id:chest_data key:items.<[loop_index]>.custom_data value:<[item].custom_data||<map>>
        - yaml set id:chest_data key:items.<[loop_index]>.full_item_string value:<[item]>
    
    # Set final count
    - yaml set id:chest_data key:non_empty_items value:<[item_count]>
    
    # Save the file
    - yaml savefile id:chest_data <[filename]>
    - yaml unload id:chest_data
    
    # Also create a simple text backup
    - define text_filename chest_data_<[timestamp]>.txt
    - define text_content "Chest Data Export<&nl>==================<&nl>Timestamp: <util.time_now><&nl>Location: <[chest_location]><&nl>World: <[chest_location].world.name><&nl>Player: <player.name><&nl>Total Items: <[item_count]><&nl><&nl>Items:<&nl>"
    
    # Add each item to text content
    - foreach <[chest_items]> as:item:
        - if <[item].material> != air:
            - define namespace <[item].material.name.before[:]||minecraft>
            - define material_name <[item].material.name.after[:]||<[item].material.name>>
            - define text_content "<[text_content]>Slot <[loop_index]>: <[namespace]>:<[material_name]> x<[item].quantity><&nl>"
    
    # Save text file
    - ~filewrite path:<[text_filename]> data:<[text_content]>
    
    # Notify player
    - narrate "<&a>Successfully saved <[item_count]> items from chest!"
    - narrate "<&7>Location: <[chest_location]>"
    - narrate "<&7>YAML file: plugins/Denizen/scripts/<[filename]>"
    - narrate "<&7>Text file: plugins/Denizen/scripts/<[text_filename]>"
    - narrate "<&7>Use '/ex yaml load id:chest_data <[filename]>' to load the data"

# Simple command to run the script
simple_chest_read_command:
    type: command
    name: simplechest
    description: Simple chest reader that saves to YAML
    usage: /simplechest
    permission: denizen.readchest
    script:
    - run simple_chest_reader

# Even simpler version that just outputs to chat
chat_chest_reader:
    type: task
    debug: false
    script:
    # Get the chest the player is looking at
    - define chest_location <player.cursor_on>
    
    # Check if the location has an inventory
    - if !<[chest_location].has_inventory>:
        - narrate "<&c>You must be looking at a chest or container!"
        - stop
    
    # Get the inventory of the chest
    - define chest_inventory <[chest_location].inventory>
    - define chest_items <[chest_inventory].list_contents>
    
    # Check if chest is empty
    - if <[chest_items].is_empty>:
        - narrate "<&c>The chest is empty!"
        - stop
    
    # Display header
    - narrate "<&a>===== Chest Contents ====="
    - narrate "<&7>Location: <[chest_location]>"
    - narrate "<&7>World: <[chest_location].world.name>"
    
    # Process each item
    - define item_count 0
    - foreach <[chest_items]> as:item:
        - if <[item].material> != air:
            - define item_count <[item_count].add[1]>
            - define namespace <[item].material.name.before[:]||minecraft>
            - define material_name <[item].material.name.after[:]||<[item].material.name>>
            - narrate "<&e>Slot <[loop_index]>: <&f><[namespace]>:<[material_name]> <&7>x<[item].quantity>"
            - if <[item].display> != "":
                - narrate "  <&7>Display: <[item].display>"
            - if !<[item].enchantment_map.is_empty>:
                - narrate "  <&7>Enchantments: <[item].enchantment_map>"
    
    - narrate "<&a>Total items: <[item_count]>"

# Chat command
chat_chest_command:
    type: command
    name: chestinfo
    description: Display chest contents in chat
    usage: /chestinfo
    permission: denizen.readchest
    script:
    - run chat_chest_reader
